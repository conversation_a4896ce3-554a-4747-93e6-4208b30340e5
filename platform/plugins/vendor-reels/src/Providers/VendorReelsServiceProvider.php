<?php

namespace Bo<PERSON>ble\VendorReels\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\VendorReels\Models\VendorReel;
use Bo<PERSON>ble\VendorReels\Repositories\Eloquent\VendorReelRepository;
use Botble\VendorReels\Repositories\Interfaces\VendorReelInterface;
use Illuminate\Routing\Events\RouteMatched;

class VendorReelsServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(VendorReelInterface::class, function () {
            return new VendorReelRepository(new VendorReel());
        });
    }

    public function boot(): void
    {
        $this->setNamespace('plugins/vendor-reels')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'general', 'messaging'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes(['vendor', 'web', 'api', 'admin']);

        // تسجيل عناصر القائمة مباشرة
        $this->registerVendorDashboardMenus();
        $this->registerAdminDashboardMenus();

        $this->app['events']->listen(RouteMatched::class, function () {
            $this->extendModels();
        });

        // تحميل helper functions
        if (file_exists($helpersFile = __DIR__ . '/../../helpers/helpers.php')) {
            require_once $helpersFile;
        }

        // تسجيل الـ Commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Botble\VendorReels\Commands\CleanupOldReelsCommand::class,
            ]);
        }

        // تسجيل Event Listeners للريلز
        $this->app['events']->listen(
            \Botble\Base\Events\CreatedContentEvent::class,
            [\Botble\VendorReels\Listeners\ReelEventListener::class, 'handleReelCreated']
        );

        $this->app['events']->listen(
            \Botble\Base\Events\UpdatedContentEvent::class,
            [\Botble\VendorReels\Listeners\ReelEventListener::class, 'handleReelUpdated']
        );

        // تسجيل Event Listeners للمحادثات
        $this->app['events']->listen(
            \Botble\VendorReels\Events\MessageSent::class,
            \Botble\VendorReels\Listeners\SendMessageNotification::class
        );

        $this->app['events']->listen(
            \Botble\VendorReels\Events\ConversationCreated::class,
            function ($event) {
                // يمكن إضافة معالجة إضافية هنا
            }
        );
    }

    protected function registerVendorDashboardMenus(): void
    {
        // التأكد من وجود إضافة Marketplace
        if (!is_plugin_active('marketplace')) {
            return;
        }

        // استخدام Facade بدلاً من Container
        \Botble\Base\Facades\DashboardMenu::for('vendor')->beforeRetrieving(function () {
            \Botble\Base\Facades\DashboardMenu::make()
                ->registerItem([
                    'id' => 'marketplace.vendor.reels',
                    'priority' => 10,
                    'name' => 'الريلز',
                    'url' => fn () => route('marketplace.vendor.reels.index'),
                    'icon' => 'ti ti-video',
                ])
                ->registerItem([
                    'id' => 'marketplace.vendor.conversations',
                    'priority' => 11,
                    'name' => 'المحادثات',
                    'url' => fn () => route('marketplace.vendor.conversations.index'),
                    'icon' => 'ti ti-message-circle',
                ]);
        });
    }

    protected function registerAdminDashboardMenus(): void
    {
        \Botble\Base\Facades\DashboardMenu::default()->beforeRetrieving(function () {
            \Botble\Base\Facades\DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-vendor-reels',
                    'priority' => 125,
                    'parent_id' => null,
                    'name' => 'إدارة الريلز',
                    'icon' => 'ti ti-video',
                    'url' => url('admin/vendor-reels'),
                    'permissions' => ['vendor-reels.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-vendor-reels-list',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-vendor-reels',
                    'name' => 'جميع الريلز',
                    'icon' => 'ti ti-list',
                    'url' => url('admin/vendor-reels'),
                    'permissions' => ['vendor-reels.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-vendor-reels-statistics',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-vendor-reels',
                    'name' => 'إحصائيات الريلز',
                    'icon' => 'ti ti-chart-bar',
                    'url' => url('admin/vendor-reels/statistics'),
                    'permissions' => ['vendor-reels.statistics'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-vendor-reels-settings',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-vendor-reels',
                    'name' => 'إعدادات الريلز',
                    'icon' => 'ti ti-settings',
                    'url' => url('admin/vendor-reels/settings'),
                    'permissions' => ['vendor-reels.settings'],
                ]);
        });
    }

    protected function extendModels(): void
    {
        // إضافة العلاقة للـ Store model
        \Botble\Marketplace\Models\Store::resolveRelationUsing('reels', function ($storeModel) {
            return $storeModel->hasMany(\Botble\VendorReels\Models\VendorReel::class, 'store_id');
        });

        // إضافة العلاقة للـ Product model
        \Botble\Ecommerce\Models\Product::resolveRelationUsing('reels', function ($productModel) {
            return $productModel->belongsToMany(\Botble\VendorReels\Models\VendorReel::class, 'reel_products', 'product_id', 'reel_id');
        });

        // إضافة العلاقات للـ Customer model للمحادثات
        \Botble\Ecommerce\Models\Customer::resolveRelationUsing('conversations', function ($customerModel) {
            return $customerModel->belongsToMany(\Botble\VendorReels\Models\Conversation::class, 'conversation_participants', 'customer_id', 'conversation_id')
                ->withPivot(['joined_at', 'left_at', 'is_admin', 'is_muted'])
                ->withTimestamps();
        });

        \Botble\Ecommerce\Models\Customer::resolveRelationUsing('sentMessages', function ($customerModel) {
            return $customerModel->hasMany(\Botble\VendorReels\Models\Message::class, 'sender_id');
        });

        // إضافة العلاقة للـ Store model للمحادثات
        \Botble\Marketplace\Models\Store::resolveRelationUsing('conversations', function ($storeModel) {
            return $storeModel->hasMany(\Botble\VendorReels\Models\Conversation::class, 'store_id');
        });
    }
}
